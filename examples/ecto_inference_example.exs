#!/usr/bin/env elixir

# Simple executable example showing Ecto inference with Drops.Operations
# This script demonstrates command operations for creating users with embedded addresses
# using Drops.Operations with Ecto schema inference and actual database operations.

# Load the project dependencies
Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"},
  {:drops, path: "."}
])

# Configure the test repository for SQLite in-memory database
Application.put_env(:example_app, Example.Repo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000
)

# Configure Ecto repos
Application.put_env(:example_app, :ecto_repos, [Example.Repo])

# Define the repository
defmodule Example.Repo do
  @moduledoc """
  Example repository using SQLite in-memory database.
  """
  use Ecto.Repo,
    otp_app: :example_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Example.User do
  @moduledoc """
  User schema with name, email, and address stored as JSON.
  """
  use Ecto.Schema
  import Ecto.Changeset

  schema "users" do
    field(:name, :string)
    field(:email, :string)

    embeds_one(:address, Address) do
      field(:street, :string)
      field(:city, :string)
      field(:state, :string)
      field(:zip_code, :string)
      field(:country, :string)
    end

    timestamps()
  end

  @doc """
  Changeset for creating and updating users.
  """
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email, :address])
    |> validate_required([:name])
    |> validate_format(:email, ~r/@/, message: "must be a valid email")
    |> validate_address()
  end
end

defmodule Example.App do
  use Drops.Operations
end

defmodule Example.CreateUser do
  use Example.App, :command

  schema(Example.User)

  @impl true
  def perform(params) do
    Example.Repo.insert(changedset(params))
  end
end

{:ok, _} = Application.ensure_all_started(:ecto_sql)
{:ok, _} = Example.Repo.start_link()

Ecto.Adapters.SQL.Sandbox.mode(Example.Repo, :manual)
:ok = Ecto.Adapters.SQL.Sandbox.checkout(Example.Repo)

Ecto.Adapters.SQL.query!(Example.Repo, """
  CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    email TEXT,
    address JSON,
    inserted_at DATETIME,
    updated_at DATETIME
  )
""")

valid_user_data = %{
  name: "John Doe",
  email: "<EMAIL>",
  address: %{
    street: "123 Main St",
    city: "Anytown",
    state: "CA",
    zip_code: "12345",
    country: "USA"
  }
}

case Example.CreateUser.execute(valid_user_data) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Verify the user was actually inserted by querying the database
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end

user_data_with_extra = %{
  name: "Jane Smith",
  email: "<EMAIL>",
  address: %{
    street: "456 Oak Ave",
    city: "Springfield",
    state: "IL",
    zip_code: "62701"
  },
  age: 28,
  extra_field: "this should be ignored"
}

IO.puts("\nExecuting CreateUser operation with extra fields:")
case Example.CreateUser.execute(user_data_with_extra) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Show current database state
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end
