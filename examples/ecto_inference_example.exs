#!/usr/bin/env elixir

# Simple executable example showing Ecto inference with Drops.Operations
# This script demonstrates command operations for creating users with embedded addresses
# using Drops.Operations with Ecto schema inference and actual database operations.

# Load the project dependencies
Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"},
  {:drops, path: "."}
])

# Let's test with a simple schema first to see if the issue is with embedded schemas
defmodule Example.SimpleUser do
  use Ecto.Schema
  import Ecto.Changeset

  schema "simple_users" do
    field(:name, :string)
    field(:email, :string)
    timestamps()
  end

  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email])
    |> validate_required([:name])
  end
end

# Configure the test repository for SQLite in-memory database
Application.put_env(:example_app, Example.Repo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000
)

# Configure Ecto repos
Application.put_env(:example_app, :ecto_repos, [Example.Repo])

# Define the repository
defmodule Example.Repo do
  @moduledoc """
  Example repository using SQLite in-memory database.
  """
  use Ecto.Repo,
    otp_app: :example_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Example.User do
  @moduledoc """
  User schema with name, email, and address stored as JSON.
  """
  use Ecto.Schema
  import Ecto.Changeset

  schema "users" do
    field(:name, :string)
    field(:email, :string)

    embeds_one(:address, Address) do
      field(:street, :string)
      field(:city, :string)
      field(:state, :string)
      field(:zip_code, :string)
      field(:country, :string)
    end

    timestamps()
  end

  @doc """
  Changeset for creating and updating users.
  """
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email, :address])
    |> validate_required([:name])
    |> validate_format(:email, ~r/@/, message: "must be a valid email")
  end
end

defmodule Example.App do
  use Drops.Operations
end

defmodule Example.CreateUser do
  use Example.App, :command

  # Debug: Let's check if Example.User is an Ecto schema
  IO.puts("Example.User module loaded: #{Code.ensure_loaded?(Example.User)}")
  IO.puts("Example.User has __schema__/1: #{function_exported?(Example.User, :__schema__, 1)}")

  # Let's test the schema inference directly
  IO.puts("Testing direct schema inference:")
  IO.puts("Has custom compiler: #{Drops.Schema.has_custom_compiler?(Example.User)}")

  # Test the protocol directly
  try do
    result = Drops.Schema.Compiler.compile(Example.User, %{}, [])
    IO.inspect(result, label: "Direct protocol call result")
  rescue
    e -> IO.inspect(e, label: "Direct protocol call error")
  end

  # Test just the inference part
  schema_ast = Drops.Schema.Inference.infer_schema(Example.User, [])
  IO.inspect(schema_ast, label: "Schema AST")

  # Test the compilation part
  direct_schema = Drops.Schema.infer_and_compile(Example.User, [])
  IO.inspect(direct_schema, label: "Direct schema")
  IO.inspect(direct_schema.meta, label: "Direct schema meta")

  schema(Example.User)

  # Debug: Let's check what the schema looks like
  def debug_schema do
    IO.inspect(schema(), label: "Schema")
    IO.inspect(schema().meta, label: "Schema meta")
  end

  @impl true
  def perform(params) do
    debug_schema()
    Example.Repo.insert(changeset(params))
  end
end

{:ok, _} = Application.ensure_all_started(:ecto_sql)
{:ok, _} = Example.Repo.start_link()

Ecto.Adapters.SQL.Sandbox.mode(Example.Repo, :manual)
:ok = Ecto.Adapters.SQL.Sandbox.checkout(Example.Repo)

Ecto.Adapters.SQL.query!(Example.Repo, """
  CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    email TEXT,
    address JSON,
    inserted_at DATETIME,
    updated_at DATETIME
  )
""")

valid_user_data = %{
  name: "John Doe",
  email: "<EMAIL>",
  address: %{
    street: "123 Main St",
    city: "Anytown",
    state: "CA",
    zip_code: "12345",
    country: "USA"
  }
}

case Example.CreateUser.execute(valid_user_data) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Verify the user was actually inserted by querying the database
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end

user_data_with_extra = %{
  name: "Jane Smith",
  email: "<EMAIL>",
  address: %{
    street: "456 Oak Ave",
    city: "Springfield",
    state: "IL",
    zip_code: "62701"
  },
  age: 28,
  extra_field: "this should be ignored"
}

IO.puts("\nExecuting CreateUser operation with extra fields:")
case Example.CreateUser.execute(user_data_with_extra) do
  {:ok, %{result: result, params: params}} ->
    IO.puts("✓ Success:")
    IO.puts("  Validated params: #{inspect(params)}")
    IO.puts("  Created user in database: #{inspect(result)}")

    # Show current database state
    user_count = Example.Repo.aggregate(Example.User, :count, :id)
    IO.puts("  Total users in database: #{user_count}")
  {:error, %{result: errors, params: params}} ->
    IO.puts("✗ Operation failed:")
    IO.puts("  Input params: #{inspect(params)}")
    IO.puts("  Errors: #{inspect(errors)}")
end
