defmodule Drops.OperationsTest do
  use Drops.DataCase, async: true

  defmodule TestApp do
    use Drops.Operations
  end

  test "it works without schema" do
    defmodule CreateUser do
      use TestApp, :command

      @impl true
      def perform(params) do
        if params[:name] == nil do
          {:error, "name is required"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} = CreateUser.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}
  end

  test "it works with a schema" do
    defmodule CreateUserWithSchema do
      use TestApp, :command

      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def perform(params) do
        if params[:name] != "<PERSON>" do
          {:error, "name is not expected"}
        else
          {:ok, params}
        end
      end
    end

    {:ok, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: "<PERSON>"})

    assert result == %{name: "<PERSON>"}
    assert params == %{name: "<PERSON>"}

    {:error, %{result: result, params: params}} =
      CreateUserWithSchema.execute(%{name: ""})

    assert_errors(["name must be filled"], {:error, result})
    assert params == %{name: ""}
  end

  test "it works with an Ecto schema" do
    # Create the users table for this test
    Ecto.Adapters.SQL.query!(Drops.TestRepo, """
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT,
        inserted_at DATETIME,
        updated_at DATETIME
      )
    """)

    defmodule CreateUserWithEctoSchema do
      use TestApp, :command

      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case TestRepo.insert(changeset(params)) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    {:ok, %{result: result, params: params}} =
      CreateUserWithEctoSchema.execute(%{name: "Jane Doe"})

    assert result == %{name: "Jane Doe"}
    assert params == %{name: "Jane Doe"}
  end
end
